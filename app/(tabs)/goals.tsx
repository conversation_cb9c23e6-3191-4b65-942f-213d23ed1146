import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Modal,
  Alert,
  FlatList,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Plus,
  Target,
  Calendar,
  Flag,
  CircleCheck as CheckCircle2,
  Circle,
  CreditCard as Edit3,
  Trash2,
  X,
  CircleAlert as AlertCircle,
  Search,
  Filter,
  Clock,
  TrendingUp,
  BookOpen,
  Star,
  CheckCircle,
  PlayCircle,
  BarChart3,
  Settings,
  Tag,
  Calendar as CalendarIcon,
  ChevronDown,
  ChevronRight,
  MoreVertical
} from 'lucide-react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  runOnJS,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import IsotopeLogo from '@/components/IsotopeLogo';
import SubjectPicker from '@/components/SubjectPicker';
import { useTasks } from '@/hooks/useTasks';
import { useSubjects } from '@/hooks/useSubjects';
import { Task, TaskCategory, TaskFilters } from '@/types/app';

const { width } = Dimensions.get('window');

// TaskCard Component
interface TaskCardProps {
  task: Task;
  onToggleComplete: () => void;
  onEdit: () => void;
  onDelete: () => void;
  onUpdateProgress: (progress: number) => void;
  getCategoryColor: (category: string) => string;
  getPriorityColor: (priority: Task['priority']) => string;
  getPriorityIcon: (priority: Task['priority']) => React.ReactNode;
  getStatusIcon: (status: Task['status']) => React.ReactNode;
  formatDate: (date: Date) => string;
  isOverdue: (date: Date) => boolean;
  getSubjectById: (id: string) => any;
}

const TaskCard: React.FC<TaskCardProps> = ({
  task,
  onToggleComplete,
  onEdit,
  onDelete,
  onUpdateProgress,
  getCategoryColor,
  getPriorityColor,
  getPriorityIcon,
  getStatusIcon,
  formatDate,
  isOverdue,
  getSubjectById,
}) => {
  const subject = task.subject_id ? getSubjectById(task.subject_id) : null;
  const isTaskOverdue = task.due_date && isOverdue(task.due_date);

  return (
    <View style={[
      styles.taskCard,
      isTaskOverdue && styles.overdueCard,
      task.status === 'completed' && styles.completedCard
    ]}>
      {/* Task Header */}
      <View style={styles.taskHeader}>
        <TouchableOpacity onPress={onToggleComplete} style={styles.statusButton}>
          {getStatusIcon(task.status)}
        </TouchableOpacity>

        <View style={styles.taskInfo}>
          <View style={styles.taskTitleRow}>
            <Text style={[
              styles.taskTitle,
              task.status === 'completed' && styles.completedTitle
            ]}>
              {task.title}
            </Text>
            {task.is_milestone && (
              <View style={styles.milestoneTag}>
                <Star size={12} color="#F59E0B" />
              </View>
            )}
          </View>

          {task.description && (
            <Text style={[
              styles.taskDescription,
              task.status === 'completed' && styles.completedDescription
            ]}>
              {task.description}
            </Text>
          )}

          {/* Tags */}
          {task.tags.length > 0 && (
            <View style={styles.tagsContainer}>
              {task.tags.slice(0, 3).map((tag, index) => (
                <View key={index} style={styles.tag}>
                  <Text style={styles.tagText}>{tag}</Text>
                </View>
              ))}
              {task.tags.length > 3 && (
                <Text style={styles.moreTagsText}>+{task.tags.length - 3}</Text>
              )}
            </View>
          )}

          {/* Subject */}
          {subject && (
            <View style={styles.subjectTag}>
              <View style={[styles.subjectDot, { backgroundColor: subject.color }]} />
              <Text style={styles.subjectName}>{subject.name}</Text>
            </View>
          )}
        </View>

        <TouchableOpacity onPress={onEdit} style={styles.editButton}>
          <MoreVertical size={16} color="#6B7280" />
        </TouchableOpacity>
      </View>

      {/* Progress Bar */}
      {task.progress_percentage > 0 && (
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View
              style={[
                styles.progressFill,
                {
                  width: `${task.progress_percentage}%`,
                  backgroundColor: getPriorityColor(task.priority)
                }
              ]}
            />
          </View>
          <Text style={styles.progressText}>{task.progress_percentage}%</Text>
        </View>
      )}

      {/* Task Meta */}
      <View style={styles.taskMeta}>
        <View style={styles.taskMetaLeft}>
          {/* Category */}
          <View style={styles.categoryTag}>
            <View style={[styles.categoryDot, { backgroundColor: getCategoryColor(task.category) }]} />
            <Text style={styles.categoryText}>{task.category}</Text>
          </View>

          {/* Priority */}
          <View style={styles.priorityTag}>
            {getPriorityIcon(task.priority)}
            <Text style={[styles.priorityText, { color: getPriorityColor(task.priority) }]}>
              {task.priority}
            </Text>
          </View>
        </View>

        <View style={styles.taskMetaRight}>
          {/* Due Date */}
          {task.due_date && (
            <View style={styles.dueDateContainer}>
              <CalendarIcon size={14} color={isTaskOverdue ? '#EF4444' : '#6B7280'} />
              <Text style={[
                styles.dueDateText,
                isTaskOverdue && styles.overdueDateText
              ]}>
                {formatDate(task.due_date)}
              </Text>
            </View>
          )}

          {/* Estimated Duration */}
          {task.estimated_duration && (
            <View style={styles.durationContainer}>
              <Clock size={14} color="#6B7280" />
              <Text style={styles.durationText}>{task.estimated_duration}m</Text>
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

export default function GoalsScreen() {
  const {
    tasks,
    categories,
    loading,
    error,
    createTask,
    updateTask,
    deleteTask,
    completeTask,
    updateTaskProgress,
    createCategory,
    getTasksByStatus,
    getTasksByCategory,
    getOverdueTasks,
    getUpcomingTasks,
    filterTasks,
    refreshTasks
  } = useTasks();

  const { getSubjectById } = useSubjects();

  // Modal states
  const [showAddModal, setShowAddModal] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | null>(null);

  // Form states
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [dueDate, setDueDate] = useState('');
  const [priority, setPriority] = useState<'low' | 'medium' | 'high' | 'urgent'>('medium');
  const [category, setCategory] = useState('general');
  const [estimatedDuration, setEstimatedDuration] = useState('');
  const [selectedSubject, setSelectedSubject] = useState<any>(null);
  const [tags, setTags] = useState<string[]>([]);
  const [isMilestone, setIsMilestone] = useState(false);

  // Filter states
  const [searchQuery, setSearchQuery] = useState('');
  const [activeFilters, setActiveFilters] = useState<TaskFilters>({});
  const [selectedView, setSelectedView] = useState<'all' | 'todo' | 'in_progress' | 'completed'>('all');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // UI states
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());
  const [showStats, setShowStats] = useState(false);

  const modalScale = useSharedValue(0);
  const filterModalScale = useSharedValue(0);

  // Reset form function
  const resetForm = () => {
    setTitle('');
    setDescription('');
    setDueDate('');
    setPriority('medium');
    setCategory('general');
    setEstimatedDuration('');
    setSelectedSubject(null);
    setTags([]);
    setIsMilestone(false);
  };

  const openAddModal = () => {
    setEditingTask(null);
    resetForm();
    setShowAddModal(true);
    modalScale.value = withSpring(1);
  };

  const openEditModal = (task: Task) => {
    setEditingTask(task);
    setTitle(task.title);
    setDescription(task.description || '');
    setDueDate(task.due_date ? task.due_date.toISOString().split('T')[0] : '');
    setPriority(task.priority);
    setCategory(task.category);
    setEstimatedDuration(task.estimated_duration?.toString() || '');
    setSelectedSubject(task.subject_id ? getSubjectById(task.subject_id) : null);
    setTags(task.tags);
    setIsMilestone(task.is_milestone);
    setShowAddModal(true);
    modalScale.value = withSpring(1);
  };

  const closeModal = () => {
    modalScale.value = withSpring(0, {}, () => {
      runOnJS(setShowAddModal)(false);
    });
  };

  const openFilterModal = () => {
    setShowFilterModal(true);
    filterModalScale.value = withSpring(1);
  };

  const closeFilterModal = () => {
    filterModalScale.value = withSpring(0, {}, () => {
      runOnJS(setShowFilterModal)(false);
    });
  };

  const handleSave = async () => {
    if (!title.trim()) {
      Alert.alert('Error', 'Please enter a task title');
      return;
    }

    const taskData = {
      title: title.trim(),
      description: description.trim(),
      category,
      priority,
      due_date: dueDate ? new Date(dueDate) : undefined,
      estimated_duration: estimatedDuration ? parseInt(estimatedDuration) : undefined,
      subject_id: selectedSubject?.id,
      tags,
      is_milestone: isMilestone,
    };

    try {
      if (editingTask) {
        await updateTask(editingTask.id, taskData);
      } else {
        await createTask(taskData);
      }
      closeModal();
    } catch (error) {
      Alert.alert('Error', 'Failed to save task. Please try again.');
    }
  };

  const handleDelete = (task: Task) => {
    Alert.alert(
      'Delete Task',
      `Are you sure you want to delete "${task.title}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteTask(task.id);
            } catch (error) {
              Alert.alert('Error', 'Failed to delete task. Please try again.');
            }
          },
        },
      ]
    );
  };

  const handleToggleComplete = async (task: Task) => {
    try {
      if (task.status === 'completed') {
        await updateTask(task.id, {
          status: 'todo',
          progress_percentage: 0,
          completion_date: undefined
        });
      } else {
        await completeTask(task.id);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to update task. Please try again.');
    }
  };

  // Utility functions
  const getPriorityColor = (priority: 'low' | 'medium' | 'high' | 'urgent') => {
    switch (priority) {
      case 'urgent': return '#DC2626';
      case 'high': return '#EF4444';
      case 'medium': return '#F59E0B';
      case 'low': return '#10B981';
      default: return '#6B7280';
    }
  };

  const getPriorityIcon = (priority: 'low' | 'medium' | 'high' | 'urgent') => {
    switch (priority) {
      case 'urgent': return <AlertCircle size={16} color="#DC2626" />;
      case 'high': return <Flag size={16} color="#EF4444" />;
      case 'medium': return <Clock size={16} color="#F59E0B" />;
      case 'low': return <Circle size={16} color="#10B981" />;
      default: return <Circle size={16} color="#6B7280" />;
    }
  };

  const getStatusColor = (status: Task['status']) => {
    switch (status) {
      case 'completed': return '#10B981';
      case 'in_progress': return '#3B82F6';
      case 'cancelled': return '#6B7280';
      default: return '#F59E0B';
    }
  };

  const getStatusIcon = (status: Task['status']) => {
    switch (status) {
      case 'completed': return <CheckCircle size={20} color="#10B981" />;
      case 'in_progress': return <PlayCircle size={20} color="#3B82F6" />;
      case 'cancelled': return <X size={20} color="#6B7280" />;
      default: return <Circle size={20} color="#F59E0B" />;
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const isOverdue = (date: Date) => {
    return date < new Date() && date.toDateString() !== new Date().toDateString();
  };

  const getCategoryColor = (categoryName: string) => {
    const category = categories.find(c => c.name === categoryName);
    return category?.color || '#6366F1';
  };

  // Filter and sort tasks
  const getFilteredTasks = () => {
    let filtered = tasks;

    // Apply view filter
    if (selectedView !== 'all') {
      filtered = filtered.filter(task => task.status === selectedView);
    }

    // Apply category filter
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(task => task.category === selectedCategory);
    }

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(task =>
        task.title.toLowerCase().includes(query) ||
        task.description?.toLowerCase().includes(query) ||
        task.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    return filtered.sort((a, b) => {
      // Sort by priority first (urgent > high > medium > low)
      const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      if (priorityDiff !== 0) return priorityDiff;

      // Then by due date (closest first)
      if (a.due_date && b.due_date) {
        return a.due_date.getTime() - b.due_date.getTime();
      }
      if (a.due_date) return -1;
      if (b.due_date) return 1;

      // Finally by creation date (newest first)
      return b.created_at.getTime() - a.created_at.getTime();
    });
  };

  const modalAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: modalScale.value }],
  }));

  const filterModalAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: filterModalScale.value }],
  }));

  // Get filtered and organized data
  const filteredTasks = getFilteredTasks();
  const overdueTasks = getOverdueTasks();
  const upcomingTasks = getUpcomingTasks();
  const todoTasks = getTasksByStatus('todo');
  const inProgressTasks = getTasksByStatus('in_progress');
  const completedTasks = getTasksByStatus('completed');

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={['#F8FAFC', '#EEF2FF', '#F8FAFC']}
        style={styles.headerGradient}
      >
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <IsotopeLogo size="medium" />
            <Text style={styles.subtitle}>Task Management</Text>
          </View>
          <View style={styles.headerRight}>
            <TouchableOpacity style={styles.statsButton} onPress={() => setShowStats(!showStats)}>
              <BarChart3 size={20} color="#6366F1" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.filterButton} onPress={openFilterModal}>
              <Filter size={20} color="#6366F1" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.addButton} onPress={openAddModal}>
              <Plus size={20} color="#FFFFFF" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchBar}>
            <Search size={20} color="#6B7280" />
            <TextInput
              style={styles.searchInput}
              placeholder="Search tasks..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholderTextColor="#9CA3AF"
            />
          </View>
        </View>

        {/* Quick Stats */}
        {showStats && (
          <Animated.View style={styles.statsContainer}>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>{todoTasks.length}</Text>
              <Text style={styles.statLabel}>To Do</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>{inProgressTasks.length}</Text>
              <Text style={styles.statLabel}>In Progress</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>{completedTasks.length}</Text>
              <Text style={styles.statLabel}>Completed</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={[styles.statNumber, { color: '#EF4444' }]}>{overdueTasks.length}</Text>
              <Text style={styles.statLabel}>Overdue</Text>
            </View>
          </Animated.View>
        )}
      </LinearGradient>

      {/* Filter Tabs */}
      <View style={styles.filterTabs}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.tabsContainer}>
          {['all', 'todo', 'in_progress', 'completed'].map((view) => (
            <TouchableOpacity
              key={view}
              style={[
                styles.filterTab,
                selectedView === view && styles.activeFilterTab
              ]}
              onPress={() => setSelectedView(view as any)}
            >
              <Text style={[
                styles.filterTabText,
                selectedView === view && styles.activeFilterTabText
              ]}>
                {view === 'all' ? 'All' :
                 view === 'todo' ? 'To Do' :
                 view === 'in_progress' ? 'In Progress' : 'Completed'}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Category Filter */}
      <View style={styles.categoryFilter}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <TouchableOpacity
            style={[
              styles.categoryChip,
              selectedCategory === 'all' && styles.activeCategoryChip
            ]}
            onPress={() => setSelectedCategory('all')}
          >
            <Text style={[
              styles.categoryChipText,
              selectedCategory === 'all' && styles.activeCategoryChipText
            ]}>
              All Categories
            </Text>
          </TouchableOpacity>
          {categories.map((category) => (
            <TouchableOpacity
              key={category.id}
              style={[
                styles.categoryChip,
                selectedCategory === category.name && styles.activeCategoryChip
              ]}
              onPress={() => setSelectedCategory(category.name)}
            >
              <View style={[styles.categoryDot, { backgroundColor: category.color }]} />
              <Text style={[
                styles.categoryChipText,
                selectedCategory === category.name && styles.activeCategoryChipText
              ]}>
                {category.name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Task List */}
      <ScrollView style={styles.taskListContainer} showsVerticalScrollIndicator={false}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Loading tasks...</Text>
          </View>
        ) : filteredTasks.length === 0 ? (
          <View style={styles.emptyState}>
            <Target size={48} color="#D1D5DB" />
            <Text style={styles.emptyTitle}>No tasks found</Text>
            <Text style={styles.emptyText}>
              {searchQuery ? 'Try adjusting your search or filters' : 'Create your first task to get started!'}
            </Text>
          </View>
        ) : (
          <FlatList
            data={filteredTasks}
            keyExtractor={(item) => item.id}
            renderItem={({ item: task }) => (
              <TaskCard
                task={task}
                onToggleComplete={() => handleToggleComplete(task)}
                onEdit={() => openEditModal(task)}
                onDelete={() => handleDelete(task)}
                onUpdateProgress={(progress) => updateTaskProgress(task.id, progress)}
                getCategoryColor={getCategoryColor}
                getPriorityColor={getPriorityColor}
                getPriorityIcon={getPriorityIcon}
                getStatusIcon={getStatusIcon}
                formatDate={formatDate}
                isOverdue={isOverdue}
                getSubjectById={getSubjectById}
              />
            )}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
          />
        )}
      </ScrollView>

      {/* Add/Edit Task Modal */}
      <Modal visible={showAddModal} transparent animationType="fade">
        <View style={styles.modalOverlay}>
          <Animated.View style={[styles.modalContent, modalAnimatedStyle]}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {editingTask ? 'Edit Task' : 'Create New Task'}
              </Text>
              <TouchableOpacity onPress={closeModal}>
                <X size={24} color="#6B7280" />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.modalBody} showsVerticalScrollIndicator={false}>
              {/* Task Title */}
              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Task Title *</Text>
                <TextInput
                  style={styles.textInput}
                  value={title}
                  onChangeText={setTitle}
                  placeholder="Enter task title"
                  placeholderTextColor="#9CA3AF"
                />
              </View>

              {/* Description */}
              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Description</Text>
                <TextInput
                  style={[styles.textInput, styles.textArea]}
                  value={description}
                  onChangeText={setDescription}
                  placeholder="Enter task description"
                  placeholderTextColor="#9CA3AF"
                  multiline
                  numberOfLines={3}
                />
              </View>

              {/* Category */}
              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Category</Text>
                <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categorySelector}>
                  {categories.map((cat) => (
                    <TouchableOpacity
                      key={cat.id}
                      style={[
                        styles.categoryOption,
                        category === cat.name && styles.categoryOptionActive
                      ]}
                      onPress={() => setCategory(cat.name)}
                    >
                      <View style={[styles.categoryDot, { backgroundColor: cat.color }]} />
                      <Text style={[
                        styles.categoryOptionText,
                        category === cat.name && styles.categoryOptionActiveText
                      ]}>
                        {cat.name}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>

              {/* Due Date */}
              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Due Date</Text>
                <TextInput
                  style={styles.textInput}
                  value={dueDate}
                  onChangeText={setDueDate}
                  placeholder="YYYY-MM-DD"
                  placeholderTextColor="#9CA3AF"
                />
              </View>

              {/* Estimated Duration */}
              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Estimated Duration (minutes)</Text>
                <TextInput
                  style={styles.textInput}
                  value={estimatedDuration}
                  onChangeText={setEstimatedDuration}
                  placeholder="e.g., 60"
                  placeholderTextColor="#9CA3AF"
                  keyboardType="numeric"
                />
              </View>

              {/* Priority */}
              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Priority</Text>
                <View style={styles.prioritySelector}>
                  {(['low', 'medium', 'high', 'urgent'] as const).map((p) => (
                    <TouchableOpacity
                      key={p}
                      style={[
                        styles.priorityButton,
                        priority === p && styles.priorityButtonActive,
                        { borderColor: getPriorityColor(p) },
                      ]}
                      onPress={() => setPriority(p)}
                    >
                      {getPriorityIcon(p)}
                      <Text
                        style={[
                          styles.priorityButtonText,
                          priority === p && { color: getPriorityColor(p) },
                        ]}
                      >
                        {p.charAt(0).toUpperCase() + p.slice(1)}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              {/* Milestone Toggle */}
              <View style={styles.inputGroup}>
                <TouchableOpacity
                  style={styles.checkboxRow}
                  onPress={() => setIsMilestone(!isMilestone)}
                >
                  <View style={[styles.checkbox, isMilestone && styles.checkboxActive]}>
                    {isMilestone && <CheckCircle size={16} color="#FFFFFF" />}
                  </View>
                  <Text style={styles.checkboxLabel}>Mark as milestone</Text>
                  <Star size={16} color="#F59E0B" />
                </TouchableOpacity>
              </View>

              {/* Subject */}
              <View style={styles.inputGroup}>
                <SubjectPicker
                  selectedSubject={selectedSubject}
                  onSelectSubject={setSelectedSubject}
                />
              </View>
            </ScrollView>
            
            <View style={styles.modalActions}>
              {editingTask && (
                <TouchableOpacity
                  style={styles.deleteButton}
                  onPress={() => {
                    closeModal();
                    if (editingTask) handleDelete(editingTask);
                  }}
                >
                  <Trash2 size={16} color="#EF4444" />
                  <Text style={styles.deleteText}>Delete</Text>
                </TouchableOpacity>
              )}

              <View style={styles.actionButtons}>
                <TouchableOpacity style={styles.cancelButton} onPress={closeModal}>
                  <Text style={styles.cancelText}>Cancel</Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
                  <LinearGradient
                    colors={['#6366F1', '#8B5CF6']}
                    style={styles.saveGradient}
                  >
                    <Text style={styles.saveText}>
                      {editingTask ? 'Update' : 'Create'}
                    </Text>
                  </LinearGradient>
                </TouchableOpacity>
              </View>
            </View>
          </Animated.View>
        </View>
      </Modal>

      {/* Filter Modal */}
      <Modal visible={showFilterModal} transparent animationType="fade">
        <View style={styles.modalOverlay}>
          <Animated.View style={[styles.filterModalContent, filterModalAnimatedStyle]}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Filter Tasks</Text>
              <TouchableOpacity onPress={closeFilterModal}>
                <X size={24} color="#6B7280" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody}>
              {/* Status Filter */}
              <View style={styles.filterSection}>
                <Text style={styles.filterSectionTitle}>Status</Text>
                <View style={styles.filterOptions}>
                  {['all', 'todo', 'in_progress', 'completed'].map((status) => (
                    <TouchableOpacity
                      key={status}
                      style={[
                        styles.filterOption,
                        selectedView === status && styles.filterOptionActive
                      ]}
                      onPress={() => setSelectedView(status as any)}
                    >
                      <Text style={[
                        styles.filterOptionText,
                        selectedView === status && styles.filterOptionActiveText
                      ]}>
                        {status === 'all' ? 'All' :
                         status === 'todo' ? 'To Do' :
                         status === 'in_progress' ? 'In Progress' : 'Completed'}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              {/* Category Filter */}
              <View style={styles.filterSection}>
                <Text style={styles.filterSectionTitle}>Category</Text>
                <View style={styles.filterOptions}>
                  <TouchableOpacity
                    style={[
                      styles.filterOption,
                      selectedCategory === 'all' && styles.filterOptionActive
                    ]}
                    onPress={() => setSelectedCategory('all')}
                  >
                    <Text style={[
                      styles.filterOptionText,
                      selectedCategory === 'all' && styles.filterOptionActiveText
                    ]}>
                      All Categories
                    </Text>
                  </TouchableOpacity>
                  {categories.map((cat) => (
                    <TouchableOpacity
                      key={cat.id}
                      style={[
                        styles.filterOption,
                        selectedCategory === cat.name && styles.filterOptionActive
                      ]}
                      onPress={() => setSelectedCategory(cat.name)}
                    >
                      <View style={[styles.categoryDot, { backgroundColor: cat.color }]} />
                      <Text style={[
                        styles.filterOptionText,
                        selectedCategory === cat.name && styles.filterOptionActiveText
                      ]}>
                        {cat.name}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            </ScrollView>

            <View style={styles.modalActions}>
              <TouchableOpacity style={styles.cancelButton} onPress={closeFilterModal}>
                <Text style={styles.cancelText}>Close</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  headerGradient: {
    paddingTop: 60,
    paddingBottom: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  subtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 4,
  },
  headerLeft: {
    flex: 1,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  statsButton: {
    backgroundColor: '#FFFFFF',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  filterButton: {
    backgroundColor: '#FFFFFF',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  addButton: {
    width: 44,
    height: 44,
    backgroundColor: '#6366F1',
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingTop: 16,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: '#1F2937',
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingTop: 16,
    gap: 12,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  filterTabs: {
    paddingTop: 16,
  },
  tabsContainer: {
    paddingHorizontal: 20,
  },
  filterTab: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    marginRight: 12,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  activeFilterTab: {
    backgroundColor: '#6366F1',
    borderColor: '#6366F1',
  },
  filterTabText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  activeFilterTabText: {
    color: '#FFFFFF',
  },
  categoryFilter: {
    paddingTop: 12,
    paddingHorizontal: 20,
  },
  categoryChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 12,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  activeCategoryChip: {
    backgroundColor: '#EEF2FF',
    borderColor: '#6366F1',
  },
  categoryChipText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  activeCategoryChipText: {
    color: '#6366F1',
  },
  categoryDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  taskListContainer: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
  },
  // Task Card Styles
  taskCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  overdueCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#EF4444',
  },
  completedCard: {
    opacity: 0.7,
  },
  taskHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  statusButton: {
    marginRight: 12,
    marginTop: 2,
  },
  taskInfo: {
    flex: 1,
  },
  taskTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    flex: 1,
  },
  completedTitle: {
    textDecorationLine: 'line-through',
    color: '#6B7280',
  },
  milestoneTag: {
    marginLeft: 8,
  },
  taskDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 8,
    lineHeight: 20,
  },
  completedDescription: {
    color: '#9CA3AF',
  },
  tagsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  tag: {
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 6,
  },
  tagText: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '500',
  },
  moreTagsText: {
    fontSize: 12,
    color: '#9CA3AF',
    fontStyle: 'italic',
  },
  subjectTag: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  subjectDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  subjectName: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '500',
  },
  editButton: {
    padding: 4,
  },
  progressContainer: {
    marginBottom: 12,
  },
  progressBar: {
    height: 4,
    backgroundColor: '#F3F4F6',
    borderRadius: 2,
    marginBottom: 4,
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'right',
  },
  taskMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  taskMetaLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  taskMetaRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  categoryTag: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryText: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '500',
  },
  priorityTag: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  priorityText: {
    fontSize: 12,
    fontWeight: '500',
  },
  dueDateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  dueDateText: {
    fontSize: 12,
    color: '#6B7280',
  },
  overdueDateText: {
    color: '#EF4444',
    fontWeight: '500',
  },
  durationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  durationText: {
    fontSize: 12,
    color: '#6B7280',
  },
  statsSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statValue: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    marginVertical: 4,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
  },
  section: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 16,
  },
  goalMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  goalDate: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  goalDateText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  goalPriority: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },

  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#6B7280',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    width: '100%',
    maxWidth: 400,
    maxHeight: '90%',
  },
  filterModalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    width: '100%',
    maxWidth: 400,
    maxHeight: '80%',
  },
  categorySelector: {
    paddingVertical: 8,
  },
  categoryOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginRight: 12,
    borderRadius: 12,
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  categoryOptionActive: {
    backgroundColor: '#EEF2FF',
    borderColor: '#6366F1',
  },
  categoryOptionText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  categoryOptionActiveText: {
    color: '#6366F1',
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxActive: {
    backgroundColor: '#6366F1',
    borderColor: '#6366F1',
  },
  checkboxLabel: {
    flex: 1,
    fontSize: 16,
    color: '#1F2937',
  },
  filterSection: {
    marginBottom: 24,
  },
  filterSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
  },
  filterOptions: {
    gap: 8,
  },
  filterOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  filterOptionActive: {
    backgroundColor: '#EEF2FF',
    borderColor: '#6366F1',
  },
  filterOptionText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  filterOptionActiveText: {
    color: '#6366F1',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
  },
  modalBody: {
    padding: 20,
    maxHeight: 400,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#374151',
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#1F2937',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  prioritySelector: {
    flexDirection: 'row',
    gap: 8,
  },
  priorityButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    gap: 6,
  },
  priorityButtonActive: {
    backgroundColor: '#F3F4F6',
  },
  priorityButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  modalActions: {
    padding: 20,
    gap: 16,
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    paddingVertical: 12,
    backgroundColor: '#FEF2F2',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#FECACA',
  },
  deleteText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#EF4444',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 14,
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    alignItems: 'center',
  },
  cancelText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  saveButton: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
  },
  saveGradient: {
    paddingVertical: 14,
    alignItems: 'center',
  },
  saveText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#FFFFFF',
  },
});